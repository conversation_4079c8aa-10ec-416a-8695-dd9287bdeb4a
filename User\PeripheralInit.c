//-----------------------------------------------------------------
// ��������: 
//		��Keil uVision4����ƽ̨�»���STM32�ĳ���ģ�壬�����ʼ���Լ�����
// ��    ��: ���ǵ���
// ��ʼ����: 2014-04-24
// �������: 2014-04-24
// �޸�����:
// ��    ��: V1.0
//   - V1.0: ����ģ��
// ���Թ���: ����STM32+FPGA����ϵͳ��ƿ����塢LZE_ST LINK2��2.8��Һ����
// ˵    ��:
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// ͷ�ļ�����
//-----------------------------------------------------------------
#include <stm32f10x.h>
#include "AD9226.h"
#include "PeripheralInit.h"


//-----------------------------------------------------------------
// ���ܳ�����
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// void PeripheralInit(void)
//-----------------------------------------------------------------
// ��������: �����ʼ������
// ��ڲ���: �� 
// �� �� ֵ: ��
// ȫ�ֱ���: ��
// ע������: ��
//-----------------------------------------------------------------
void PeripheralInit(void)
{
	MAX262_GPIO_Init();
}

void MAX262_GPIO_Init(void)               // MAX262 GPIO initialization for STM32F103C8T6
{
	GPIO_InitTypeDef GPIO_InitStructure;

	// Enable GPIOA clock (F103C8T6 doesn't have GPIOE)
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);

	// Configure PA0-PA7 for MAX262 control
	// PA0=D0, PA1=D1, PA2=A0, PA3=A1, PA4=A2, PA5=A3, PA6=LE, PA7=WR
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0  | GPIO_Pin_1          // D0, D1
															| GPIO_Pin_2  | GPIO_Pin_3          // A0, A1
															| GPIO_Pin_4  | GPIO_Pin_5          // A2, A3
															| GPIO_Pin_6  | GPIO_Pin_7;         // LE, WR
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;                // Push-pull output
	GPIO_Init(GPIOA,&GPIO_InitStructure);

}

void PA8_PWM_ClockOutput_Init(void)         // Configure PA8 to output 139.8kHz PWM for MAX262 (Direct Register Access)
{
	GPIO_InitTypeDef GPIO_InitStructure;

	// Enable clocks for GPIOA and TIM1
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_TIM1, ENABLE);

	// Configure PA8 as TIM1_CH1 output (Alternate Function Push-Pull)
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;                 // Alternate function push-pull
	GPIO_Init(GPIOA, &GPIO_InitStructure);

	// Configure TIM1 using direct register access to avoid library dependencies
	// System clock = 72MHz, Target frequency = 139.8kHz
	// Timer frequency = 72MHz / (PSC + 1) / (ARR + 1)
	// Using PSC = 0, ARR = 514: 72MHz / 1 / 515 ≈ 139.8kHz

	// Reset TIM1 configuration
	TIM1->CR1 = 0;
	TIM1->CR2 = 0;

	// Set prescaler and auto-reload register
	TIM1->PSC = 0;                                                  // Prescaler = 0 (no division)
	TIM1->ARR = 514;                                                // Auto-reload = 514 (515 counts total)

	// Configure Channel 1 for PWM mode 1, 50% duty cycle
	TIM1->CCR1 = 257;                                               // Compare value = 257 (50% duty cycle)

	// Configure Channel 1 output
	// CC1S = 00 (output), OC1M = 110 (PWM mode 1), OC1PE = 1 (preload enable)
	TIM1->CCMR1 = (6 << 4) | (1 << 3);                            // OC1M = 110, OC1PE = 1

	// Enable Channel 1 output, set polarity to active high
	TIM1->CCER = (1 << 0);                                         // CC1E = 1 (enable output)

	// Enable main output (required for TIM1)
	TIM1->BDTR = (1 << 15);                                        // MOE = 1 (main output enable)

	// Enable auto-reload preload
	TIM1->CR1 |= (1 << 7);                                         // ARPE = 1

	// Start the timer
	TIM1->CR1 |= (1 << 0);                                         // CEN = 1 (counter enable)
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
