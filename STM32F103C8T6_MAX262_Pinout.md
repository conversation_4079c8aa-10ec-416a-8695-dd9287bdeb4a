# STM32F103C8T6 与 MAX262 模块引脚连接说明

## 概述
本文档说明了如何将MAX262双路可编程滤波器模块连接到STM32F103C8T6微控制器。F103C8T6是48引脚封装，没有PE口，因此所有控制引脚都重新分配到了GPIOA口。

## 引脚连接表

| MAX262模块引脚 | STM32F103C8T6引脚 | 功能说明 |
|---------------|------------------|----------|
| D0            | PA0              | 数据位0  |
| D1            | PA1              | 数据位1  |
| A0            | PA2              | 地址位0  |
| A1            | PA3              | 地址位1  |
| A2            | PA4              | 地址位2  |
| A3            | PA5              | 地址位3  |
| LE            | PA6              | 锁存使能 |
| WR            | PA7              | 写使能   |
| CLKA          | PA8              | 时钟输入A (139.8kHz PWM) |
| CLKB          | PA8              | 时钟输入B (139.8kHz PWM) |
| GND           | GND              | 公共地   |
| VCC           | 3.3V             | 电源     |

## 物理连接示意图

```
MAX262模块          STM32F103C8T6
┌─────────┐         ┌─────────────┐
│   D0    │◄────────│    PA0      │
│   D1    │◄────────│    PA1      │
│   A0    │◄────────│    PA2      │
│   A1    │◄────────│    PA3      │
│   A2    │◄────────│    PA4      │
│   A3    │◄────────│    PA5      │
│   LE    │◄────────│    PA6      │
│   WR    │◄────────│    PA7      │
│   CLK   │◄────────│    PA8      │ (MCO)
│   GND   │◄────────│    GND      │
│   VCC   │◄────────│   3.3V      │
└─────────┘         └─────────────┘
```

## 代码修改说明

### 1. 头文件修改 (MAX262.h)
- 将所有GPIO引脚定义从GPIOB/GPIOE改为GPIOA
- 数据线D0、D1分配到PA0、PA1
- 地址线A0-A3分配到PA2-PA5
- 控制线LE、WR分配到PA6、PA7

### 2. 驱动文件修改 (MAX262.c)
- 修改GPIO寄存器操作，从GPIOB改为GPIOA
- 调整位移操作以适应新的引脚分配
- 更新BRR和ODR寄存器的掩码值

### 3. 初始化文件修改 (PeripheralInit.c)
- 启用GPIOA时钟而不是GPIOB和GPIOE
- 配置PA0-PA7为推挽输出模式

### 4. 主文件修改 (main.c)
- 更新引脚连接注释
- 修改GPIO清零操作从GPIOB改为GPIOA

### 5. 时钟输出配置 (PA8_PWM_ClockOutput_Init)
- 使用TIM1_CH1在PA8输出139.8kHz PWM信号
- 频率计算: 72MHz / (0+1) / (514+1) = 139.8kHz
- 50%占空比方波，适合MAX262的CLKA和CLKB输入
- 对应代码中的fclk1 = 0.1398MHz参数

## 注意事项

1. **电压兼容性**: 确保MAX262模块的工作电压与STM32的3.3V IO电压兼容
2. **时序要求**: 代码中保持了200ns的延时以满足MAX262的时序要求
3. **引脚冲突**: 使用PA0-PA7后，这些引脚不能用于其他功能
4. **调试接口**: PA13、PA14用于SWD调试，不要占用
5. **时钟信号**: PA8用于输出139.8kHz时钟，不能用于其他功能
6. **时钟频率**: 确保时钟频率与MAX262的工作要求匹配

## 测试建议

1. 首先检查硬件连接是否正确
2. 使用万用表测试各引脚的电压电平
3. 编译并下载程序到STM32F103C8T6
4. 观察MAX262的输出是否符合预期

## 故障排除

如果遇到问题，请检查：
1. 引脚连接是否正确
2. 电源供电是否稳定
3. 代码编译是否成功
4. STM32是否正常运行
5. MAX262模块是否损坏
