# PA8输出139.8kHz时钟配置说明

## 概述
本文档说明如何配置STM32F103C8T6的PA8引脚输出139.8kHz的PWM时钟信号，用于驱动MAX262滤波器芯片。

## 配置参数

### 时钟频率计算
- **系统时钟**: 72MHz
- **目标频率**: 139.8kHz (对应代码中fclk1 = 0.1398MHz)
- **定时器**: TIM1
- **通道**: Channel 1 (PA8)

### 计算公式
```
PWM频率 = 系统时钟 / (预分频器 + 1) / (自动重装载值 + 1)
139.8kHz = 72MHz / (0 + 1) / (514 + 1)
139.8kHz = 72MHz / 515 = 139,805.8Hz ≈ 139.8kHz
```

### 占空比设置
- **自动重装载值(ARR)**: 514
- **比较值(CCR)**: 257
- **占空比**: 257/515 ≈ 50%

## 代码实现

### 1. 启用定时器模块
在 `stm32f10x_conf.h` 中启用定时器模块：
```c
#include "stm32f10x_tim.h"  // 取消注释
```

### 2. GPIO配置
```c
// 配置PA8为TIM1_CH1复用功能
GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8;
GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;  // 复用推挽输出
GPIO_Init(GPIOA, &GPIO_InitStructure);
```

### 3. 定时器基本配置
```c
TIM_TimeBaseStructure.TIM_Period = 514;                    // ARR = 514
TIM_TimeBaseStructure.TIM_Prescaler = 0;                   // 不分频
TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
TIM_TimeBaseStructure.TIM_RepetitionCounter = 0;
```

### 4. PWM输出配置
```c
TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
TIM_OCInitStructure.TIM_Pulse = 257;                       // 50%占空比
TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;
```

## 引脚连接

### MAX262连接
```
MAX262模块    STM32F103C8T6
CLKA    <--   PA8 (139.8kHz PWM)
CLKB    <--   PA8 (139.8kHz PWM)
```

### 注意事项
1. **共用时钟**: MAX262的CLKA和CLKB可以共用同一个时钟源
2. **频率精度**: 实际输出频率为139,805.8Hz，误差约0.004%
3. **占空比**: 50%占空比提供标准的方波信号
4. **驱动能力**: PA8可以驱动多个时钟输入引脚

## 测试验证

### 示波器测试
1. 将示波器探头连接到PA8
2. 设置时基为10μs/div
3. 应该看到周期约7.15μs的方波信号
4. 频率应显示约139.8kHz

### 万用表测试
1. 使用具有频率测量功能的万用表
2. 连接到PA8引脚
3. 应该读取到约139.8kHz的频率

## 故障排除

### 常见问题
1. **无输出**: 检查TIM1时钟是否启用
2. **频率不对**: 验证系统时钟是否为72MHz
3. **波形异常**: 检查GPIO配置是否为复用推挽模式

### 调试建议
1. 使用示波器验证波形质量
2. 检查时钟树配置
3. 确认定时器寄存器设置正确

## 相关文件
- `User/PeripheralInit.c` - 包含PA8_PWM_ClockOutput_Init()函数
- `User/PeripheralInit.h` - 函数声明
- `User/main.c` - 主程序调用
- `CMSIS/DeviceSupport/STM32F10x/stm32f10x_conf.h` - 模块启用配置
