//-----------------------------------------------------------------
// ��������: 
//		MAX262��������
// ��    ��: ���ǵ���
// ��ʼ����: 2018-05-24
// �������: 2018-05-24
// �޸�����:
// ��    ��: V1.0
//   - V1.0: ����ģ��
// ���Թ���: ����STM32+FPGA����ϵͳ��ƿ����塢LZE_ST LINK2��MAX262ģ��
// ˵    ��:
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// ͷ�ļ�����
//-----------------------------------------------------------------
#include <stm32f10x.h>
#include "MAX262.h"
#include "delay.h"
#include <math.h>
#define fclk1   0.1398    //139.8kʱ��Ƶ��
#define fclk2   0.1398    //139.8kʱ��Ƶ��

//-----------------------------------------------------------------
// ���ܳ�����
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// u8 Fn(float f)                     
//-----------------------------------------------------------------
// ��������: ����f��Ƶ�ʿ�����N
// ��ڲ���: ��ֹƵ��(����Ƶ��) f
// �� �� ֵ: ������N
// ȫ�ֱ���: ��
// ע������: ��
//-----------------------------------------------------------------
u8 Fn1(float f)                      //Ƶ�ʹؼ���
{
    u8 temp;
    temp = (fclk1*637)-26;         //ԭ����*2/pi;��*0.637  fclk��M��λ��f��λkhz
    return temp;  	                //������оƬ�ֲ��ϵĹ�ʽ
}

u8 Fn2(float f)                      //Ƶ�ʹؼ���
{
    u8 temp;
    temp = (fclk2*637)-26;         //ԭ����*2/pi;��*0.637  fclk��M��λ��f��λkhz
    return temp;  	                //������оƬ�ֲ��ϵĹ�ʽ
}
//-----------------------------------------------------------------
// u8 Qn(float q)                     
//-----------------------------------------------------------------
// ��������: ����Q�Ŀ�����N
// ��ڲ���: Q
// �� �� ֵ: ������N
// ȫ�ֱ���: ��
// ע������: ��
//-----------------------------------------------------------------
u8 Qn(float q)                     //Ʒ�������ؼ���
{
    u8 temp;
    temp = 128-(64/q);             //���忴оƬ�ֲ����й�ʽ
    return temp; 
}
//-----------------------------------------------------------------
// void Filter1(uint8_t mode, float q)
//-----------------------------------------------------------------
// ��������: �˲�����ģʽ��Ƶ���Լ�Qֵ������
// ��ڲ���: ģʽ mode, ��ֹ/����Ƶ�� f, Qֵ q
// �� �� ֵ: ��
// ȫ�ֱ���: ��
// ע������: ��
//-----------------------------------------------------------------
void Filter1(uint8_t mode, float f, float q)
{
	uint8_t i;
	uint8_t a = 0x03;
	uint8_t sf, sq;
	i  = sf = sq = 0;
	sf = Fn1(f);                     // �����Ƶ�ʿ�����N
	sq = Qn(q);                     // ���Q��Ӧ�Ŀ�����N
	LE_H;                           // ʹ�ܶ�����
	Delay_ns(200);
	WR_H;                           // д�˿�����
	Delay_ns(200);
	
	// According to chip manual P15 Table4

	GPIOA->BRR = 0x00ff;	          // Clear PA0-PA7 for mode address
	Delay_ns(200);
	WR_L;                           // Write control
	Delay_ns(200);
// 	GPIOA->BRR = 0x0003;
	GPIOA->ODR |= ((uint16_t)(mode & 0x03) << 0);    // Send mode data to D1(PA1), D0(PA0)
	Delay_ns(200);
	WR_H;                           // Write control
	Delay_ns(200);
	
	for(i = 0; i < 3; i++)
	{
		GPIOA -> BRR = 0x00ff;	          // Clear PA0-PA7 for address
		GPIOA -> ODR |= (uint16_t)(i+1)<<2;          // Write address to A0-A3 (PA2-PA5)
		Delay_ns(200);
		WR_L;                             // Write control
		Delay_ns(200);
// 		GPIOA->BRR = 0x0003;            // Clear data bits
		GPIOA->ODR |= ((uint16_t)(sf & a) << (0+2*i));		  // Write f control value N to D0,D1
		Delay_ns(200);
		WR_H;                             // Write control
		a = a << 2;                       // Shift a left 2 bits
	}
	
	a = 0x03;
	
	for(i = 0; i < 4; i++)
	{
		GPIOA -> BRR = 0x00ff;	          // Clear PA0-PA7 for address
		GPIOA -> ODR |= (uint16_t)(i+4)<<2;          // Write address to A0-A3 (PA2-PA5)
		Delay_ns(200);
		WR_L;                             // Write control
		Delay_ns(200);
// 		GPIOA->BRR = 0x0003;              // Clear data bits
		GPIOA->ODR |= ((uint16_t)(sq & a) << (0+2*i));		  // Write Q control value N to D0,D1
		Delay_ns(200);
		WR_H;                             // Write control
		a = a << 2;                       // Shift a left 2 bits
	}
}

//-----------------------------------------------------------------
// void Filter2(uint8_t mode, float f, float q)
//-----------------------------------------------------------------
// ��������: �˲���2��ģʽ��Ƶ���Լ�Qֵ������
// ��ڲ���: ģʽ mode, ��ֹ/���� Ƶ�� f, Qֵ q
// �� �� ֵ: ��
// ȫ�ֱ���: ��
// ע������: ��
//-----------------------------------------------------------------
void Filter2(uint8_t mode, float f, float q)
{
	uint8_t i;
	uint8_t a = 0x03;
	uint8_t sf, sq;
	i  = sf = sq = 0;
	sf = Fn2(f);                     // �����Ƶ�ʿ�����N
	sq = Qn(q);                     // ���Q��Ӧ�Ŀ�����N
	LE_H;                           // ʹ�ܶ�����
	Delay_ns(200);
	WR_H;                           // д�˿�����
	Delay_ns(200);
	
	GPIOA->BRR = 0x00ff;	          // Clear PA0-PA7 for mode address
	GPIOA -> ODR |= (uint16_t)(i+8)<<2;          // Write address to A0-A3 (PA2-PA5)
	Delay_ns(200);
	WR_L;                           // Write control
	Delay_ns(200);
// 	GPIOA->BRR = 0x0003;
	GPIOA->ODR |= ((uint16_t)(mode & 0x03) << 0);    // Send mode data to D1(PA1), D0(PA0)
	Delay_ns(200);
	WR_H;                           // Write control
	Delay_ns(200);
	
	for(i = 0; i < 3; i++)
	{
		GPIOA -> BRR = 0x00ff;	          // Clear PA0-PA7 for address
		GPIOA -> ODR |= (uint16_t)(i+9)<<2;          // Write address to A0-A3 (PA2-PA5)
		Delay_ns(200);
		WR_L;                             // Write control
		Delay_ns(200);
// 		GPIOA->BRR = 0x0003;            // Clear data bits
		GPIOA->ODR |= ((uint16_t)(sf & a) << (0+2*i));		  // Write f control value N to D0,D1
		Delay_ns(200);
		WR_H;                             // Write control
		a = a << 2;                       // Shift a left 2 bits
	}
	
	a = 0x03;
	
	for(i = 0; i < 4; i++)
	{
		GPIOA -> BRR = 0x00ff;	          // Clear PA0-PA7 for address
		GPIOA -> ODR |= (uint16_t)(i+12)<<2;          // Write address to A0-A3 (PA2-PA5)
		Delay_ns(200);
		WR_L;                             // Write control
		Delay_ns(200);
		GPIOA->ODR |= ((uint16_t)(sq & a) << (0+2*i));		  // Write Q control value N to D0,D1
		Delay_ns(200);
		WR_H;                             // Write control
		a = a << 2;                       // Shift a left 2 bits
	}
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
