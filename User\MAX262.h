//-----------------------------------------------------------------
// MAX262����ͷ�ļ�
// ͷ�ļ���: MAX262.h
// ��    ��: ���ǵ���
// ��дʱ��: 2014-01-28
// �޸�����:
//-----------------------------------------------------------------

#ifndef _MAX262_H
#define _MAX262_H

//-----------------------------------------------------------------
// Pin definitions for STM32F103C8T6 (48-pin package)
//-----------------------------------------------------------------
// Data Lines - using GPIOA
#define	D0				(GPIOA,GPIO_Pin_0)
#define D0_L			GPIO_ResetBits(GPIOA,GPIO_Pin_0)
#define D0_H			GPIO_SetBits(GPIOA,GPIO_Pin_0)

#define	D1				(GPIOA,GPIO_Pin_1)
#define	D1_L			GPIO_ResetBits(GPIOA,GPIO_Pin_1)
#define	D1_H			GPIO_SetBits(GPIOA,GPIO_Pin_1)

// Address Lines - using GPIOA
#define	A0		    (GPIOA,GPIO_Pin_2)
#define	A0_L	    GPIO_ResetBits(GPIOA,GPIO_Pin_2)
#define	A0_H	    GPIO_SetBits(GPIOA,GPIO_Pin_2)
#define A0_IS_L	  (GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_2) == Bit_RESET)

#define	A1		    (GPIOA,GPIO_Pin_3)
#define	A1_L	    GPIO_ResetBits(GPIOA,GPIO_Pin_3)
#define	A1_H	    GPIO_SetBits(GPIOA,GPIO_Pin_3)
#define A1_IS_H		(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_3) == Bit_SET)

#define	A2				(GPIOA,GPIO_Pin_4)
#define	A2_L	    GPIO_ResetBits(GPIOA,GPIO_Pin_4)
#define	A2_H	    GPIO_SetBits(GPIOA,GPIO_Pin_4)

#define	A3		    (GPIOA,GPIO_Pin_5)
#define	A3_L	    GPIO_ResetBits(GPIOA,GPIO_Pin_5)
#define	A3_H	    GPIO_SetBits(GPIOA,GPIO_Pin_5)

// Control Lines - using GPIOA
#define	LE		    (GPIOA,GPIO_Pin_6)
#define	LE_L	    GPIO_ResetBits(GPIOA,GPIO_Pin_6)
#define	LE_H	    GPIO_SetBits(GPIOA,GPIO_Pin_6)

#define	WR		    (GPIOA,GPIO_Pin_7)
#define	WR_L	    GPIO_ResetBits(GPIOA,GPIO_Pin_7)
#define	WR_H	    GPIO_SetBits(GPIOA,GPIO_Pin_7)


//-----------------------------------------------------------------
// �ⲿ��������
//-----------------------------------------------------------------
extern void Filter1(u8 mode,float f, float q);
extern void Filter2(u8 mode,float f, float q);
extern u8 Fn1(float f);
extern u8 Fn2(float f);
extern u8 Qn(float q);
#endif

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
