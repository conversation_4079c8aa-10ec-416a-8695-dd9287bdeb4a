//-----------------------------------------------------------------
// ��������: 
//		��Keil uVision4����ƽ̨�»���STM32�ĳ���ģ��
// ��    ��: ���ǵ���
// ��ʼ����: 2018-05-24
// �������: 2018-05-24
// �޸�����:
// ��    ��: V1.0
//   - V1.0: ����ģ��
// ���Թ���: ����STM32���İ塢LZE_ST LINK2��MAX262ģ��
// �� �� ˵ ��:
//    MAX262 Module     STM32F103C8T6 Board
// 				WR    <--    PA7
// 				LE    <--    PA6
// 				D0    <--    PA0
// 				D1    <--    PA1
// 				A0    <--    PA2
// 				A1    <--    PA3
// 				A2    <--    PA4
// 				A3    <--    PA5
// 				CLKA  <--    PA8  (139.8kHz PWM output)
// 				CLKB  <--    PA8  (139.8kHz PWM output)
// 				GND   <-->   GND
// 				VCC   <-->   3.3V
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// ͷ�ļ����� 
//-----------------------------------------------------------------
#include <stm32f10x.h>
#include "delay.h"
#include "PeripheralInit.h"
#include "MAX262.h"


//-----------------------------------------------------------------
// ������
//-----------------------------------------------------------------
int main(void)
{
	MAX262_GPIO_Init();                   // GPIO initialization
	PA8_PWM_ClockOutput_Init();           // 139.8kHz PWM clock output on PA8
	Filter1(1,0.2539,0.178);                    // (mode) Control mode, cutoff frequency f, quality factor q
	Filter2(1,0.2539,0.178);                    // (mode) Control mode, cutoff frequency f, quality factor q
	Delay_1ms(100);
	GPIOA->ODR =0;
	while(1);
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
