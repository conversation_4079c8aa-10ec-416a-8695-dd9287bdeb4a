Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to peripheralinit.o(.text) for MAX262_GPIO_Init
    main.o(.text) refers to max262.o(.text) for Filter1
    main.o(.text) refers to delay.o(.text) for Delay_1ms
    peripheralinit.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    peripheralinit.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    max262.o(.text) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    max262.o(.text) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    max262.o(.text) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    max262.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_SetBits
    max262.o(.text) refers to delay.o(.text) for Delay_ns
    stm32f10x_gpio.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(.text) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_tim.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    system_stm32f10x.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing misc.o(.text), (220 bytes).
    Removing stm32f10x_tim.o(.text), (3610 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).

3 unused section(s) (total 3862 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ..\CMSIS\CoreSupport\core_cm3.c          0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.s 0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.c 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_tim.c 0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\User\Delay.c                          0x00000000   Number         0  delay.o ABSOLUTE
    ..\User\MAX262.c                         0x00000000   Number         0  max262.o ABSOLUTE
    ..\User\PeripheralInit.c                 0x00000000   Number         0  peripheralinit.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\\CMSIS\\CoreSupport\\core_cm3.c       0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001a4   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001a6   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001a8   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080001aa   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080001ac   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001ac   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001ac   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001b2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001b2   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001b6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001b6   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001be   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001c0   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001c0   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001c4   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001cc   Section        0  main.o(.text)
    .text                                    0x08000204   Section        0  delay.o(.text)
    .text                                    0x0800032c   Section        0  peripheralinit.o(.text)
    .text                                    0x080003dc   Section        0  max262.o(.text)
    .text                                    0x080006c4   Section        0  stm32f10x_gpio.o(.text)
    .text                                    0x08000a20   Section        0  stm32f10x_rcc.o(.text)
    .text                                    0x08000dc4   Section        0  system_stm32f10x.o(.text)
    SetSysClockTo72                          0x08000dc5   Thumb Code   214  system_stm32f10x.o(.text)
    SetSysClock                              0x08000e9b   Thumb Code     8  system_stm32f10x.o(.text)
    .text                                    0x08000fa4   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000fe4   Section        0  heapauxi.o(.text)
    .text                                    0x08000fea   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001034   Section        0  exit.o(.text)
    .text                                    0x08001048   Section        8  libspace.o(.text)
    .text                                    0x08001050   Section        0  sys_exit.o(.text)
    .text                                    0x0800105c   Section        2  use_no_semi.o(.text)
    .text                                    0x0800105e   Section        0  indicate_semi.o(.text)
    x$fpl$fadd                               0x08001060   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x0800106f   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fdiv                               0x08001124   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x08001125   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$ffixu                              0x080012a8   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$fnaninf                            0x080012e6   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08001372   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frsb                               0x0800137c   Section       20  faddsub_clz.o(x$fpl$frsb)
    x$fpl$fsub                               0x08001390   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x0800139f   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$usenofp                            0x0800147a   Section        0  usenofp.o(x$fpl$usenofp)
    .data                                    0x20000000   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000010   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000014   Section       20  system_stm32f10x.o(.data)
    .bss                                     0x20000028   Section       96  libspace.o(.bss)
    HEAP                                     0x20000088   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x20000088   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x20000288   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x20000288   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x20000688   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001a5   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001a9   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080001ad   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001ad   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001ad   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001bf   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001c5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    main                                     0x080001cd   Thumb Code    44  main.o(.text)
    TimingDelay_Decrement                    0x08000205   Thumb Code     2  delay.o(.text)
    Delay_ns                                 0x08000207   Thumb Code    12  delay.o(.text)
    Delay_1us                                0x08000213   Thumb Code    26  delay.o(.text)
    Delay_2us                                0x0800022d   Thumb Code    26  delay.o(.text)
    Delay_10us                               0x08000247   Thumb Code    36  delay.o(.text)
    Delay_250us                              0x0800026b   Thumb Code    36  delay.o(.text)
    Delay_882us                              0x0800028f   Thumb Code    26  delay.o(.text)
    Delay_1ms                                0x080002a9   Thumb Code    36  delay.o(.text)
    Delay_5ms                                0x080002cd   Thumb Code    38  delay.o(.text)
    Delay_50ms                               0x080002f3   Thumb Code    40  delay.o(.text)
    Delay                                    0x0800031b   Thumb Code    18  delay.o(.text)
    MAX262_GPIO_Init                         0x0800032d   Thumb Code    38  peripheralinit.o(.text)
    PeripheralInit                           0x08000353   Thumb Code     8  peripheralinit.o(.text)
    PA8_PWM_ClockOutput_Init                 0x0800035b   Thumb Code   120  peripheralinit.o(.text)
    Fn1                                      0x080003dd   Thumb Code     6  max262.o(.text)
    Fn2                                      0x080003e3   Thumb Code     6  max262.o(.text)
    Qn                                       0x080003e9   Thumb Code    40  max262.o(.text)
    Filter1                                  0x08000411   Thumb Code   332  max262.o(.text)
    Filter2                                  0x0800055d   Thumb Code   356  max262.o(.text)
    GPIO_DeInit                              0x080006c5   Thumb Code   172  stm32f10x_gpio.o(.text)
    GPIO_AFIODeInit                          0x08000771   Thumb Code    20  stm32f10x_gpio.o(.text)
    GPIO_Init                                0x08000785   Thumb Code   278  stm32f10x_gpio.o(.text)
    GPIO_StructInit                          0x0800089b   Thumb Code    16  stm32f10x_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x080008ab   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadInputData                       0x080008bd   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x080008c5   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputData                      0x080008d7   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_SetBits                             0x080008df   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_ResetBits                           0x080008e3   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_WriteBit                            0x080008e7   Thumb Code    10  stm32f10x_gpio.o(.text)
    GPIO_Write                               0x080008f1   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_PinLockConfig                       0x080008f5   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_EventOutputConfig                   0x08000907   Thumb Code    26  stm32f10x_gpio.o(.text)
    GPIO_EventOutputCmd                      0x08000921   Thumb Code     6  stm32f10x_gpio.o(.text)
    GPIO_PinRemapConfig                      0x08000927   Thumb Code   138  stm32f10x_gpio.o(.text)
    GPIO_EXTILineConfig                      0x080009b1   Thumb Code    66  stm32f10x_gpio.o(.text)
    GPIO_ETH_MediaInterfaceConfig            0x080009f3   Thumb Code     8  stm32f10x_gpio.o(.text)
    RCC_DeInit                               0x08000a21   Thumb Code    64  stm32f10x_rcc.o(.text)
    RCC_HSEConfig                            0x08000a61   Thumb Code    70  stm32f10x_rcc.o(.text)
    RCC_GetFlagStatus                        0x08000aa7   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x08000adf   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x08000b17   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_HSICmd                               0x08000b2b   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_PLLConfig                            0x08000b31   Thumb Code    24  stm32f10x_rcc.o(.text)
    RCC_PLLCmd                               0x08000b49   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08000b4f   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08000b61   Thumb Code    10  stm32f10x_rcc.o(.text)
    RCC_HCLKConfig                           0x08000b6b   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK1Config                          0x08000b7d   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK2Config                          0x08000b8f   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ITConfig                             0x08000ba3   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_USBCLKConfig                         0x08000bbd   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ADCCLKConfig                         0x08000bc5   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_LSEConfig                            0x08000bd7   Thumb Code    50  stm32f10x_rcc.o(.text)
    RCC_LSICmd                               0x08000c09   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08000c0f   Thumb Code    12  stm32f10x_rcc.o(.text)
    RCC_RTCCLKCmd                            0x08000c1b   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_GetClocksFreq                        0x08000c23   Thumb Code   192  stm32f10x_rcc.o(.text)
    RCC_AHBPeriphClockCmd                    0x08000ce3   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08000cfd   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x08000d17   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08000d31   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08000d4b   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_BackupResetCmd                       0x08000d65   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08000d6d   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_MCOConfig                            0x08000d73   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_ClearFlag                            0x08000d79   Thumb Code    14  stm32f10x_rcc.o(.text)
    RCC_GetITStatus                          0x08000d87   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08000d9b   Thumb Code     6  stm32f10x_rcc.o(.text)
    SystemInit                               0x08000ea3   Thumb Code    78  system_stm32f10x.o(.text)
    SystemCoreClockUpdate                    0x08000ef1   Thumb Code   142  system_stm32f10x.o(.text)
    Reset_Handler                            0x08000fa5   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    NMI_Handler                              0x08000fad   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    HardFault_Handler                        0x08000faf   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    MemManage_Handler                        0x08000fb1   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    BusFault_Handler                         0x08000fb3   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    UsageFault_Handler                       0x08000fb5   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SVC_Handler                              0x08000fb7   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    DebugMon_Handler                         0x08000fb9   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    PendSV_Handler                           0x08000fbb   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SysTick_Handler                          0x08000fbd   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART1_IRQHandler                        0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x08000fbf   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x08000fc1   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __use_two_region_memory                  0x08000fe5   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000fe7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000fe9   Thumb Code     2  heapauxi.o(.text)
    __user_setup_stackheap                   0x08000feb   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08001035   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08001049   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001049   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001049   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x08001051   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x0800105d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800105d   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x0800105f   Thumb Code     0  indicate_semi.o(.text)
    __aeabi_fadd                             0x08001061   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08001061   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __aeabi_fdiv                             0x08001125   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x08001125   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_f2uiz                            0x080012a9   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x080012a9   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __fpl_fnaninf                            0x080012e7   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08001373   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_frsub                            0x0800137d   Thumb Code     0  faddsub_clz.o(x$fpl$frsb)
    _frsb                                    0x0800137d   Thumb Code    20  faddsub_clz.o(x$fpl$frsb)
    __aeabi_fsub                             0x08001391   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08001391   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    __I$use$fp                               0x0800147a   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x0800147c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800149c   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000014   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000018   Data          16  system_stm32f10x.o(.data)
    __libspace_start                         0x20000028   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000088   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000014c4, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000149c, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          184    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO          193  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO          371    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x0000001a   Code   RO          373    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x0000001c   Code   RO          375    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x080001a4   0x00000002   Code   RO          239    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          246    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          248    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          251    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          253    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          255    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          258    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          260    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          262    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          264    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          266    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          268    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          270    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          272    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          274    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          276    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          278    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          282    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          284    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          286    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          288    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000002   Code   RO          289    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001a8   0x080001a8   0x00000002   Code   RO          309    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO          322    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO          324    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO          326    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO          329    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO          332    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO          334    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO          337    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000002   Code   RO          338    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080001ac   0x080001ac   0x00000000   Code   RO          209    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001ac   0x080001ac   0x00000000   Code   RO          216    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001ac   0x080001ac   0x00000006   Code   RO          228    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001b2   0x080001b2   0x00000000   Code   RO          218    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001b2   0x080001b2   0x00000004   Code   RO          219    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO          221    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001b6   0x080001b6   0x00000008   Code   RO          222    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001be   0x080001be   0x00000002   Code   RO          243    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO          291    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001c0   0x080001c0   0x00000004   Code   RO          292    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001c4   0x080001c4   0x00000006   Code   RO          293    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001ca   0x080001ca   0x00000002   PAD
    0x080001cc   0x080001cc   0x00000038   Code   RO            1    .text               main.o
    0x08000204   0x08000204   0x00000128   Code   RO           53    .text               delay.o
    0x0800032c   0x0800032c   0x000000b0   Code   RO           68    .text               peripheralinit.o
    0x080003dc   0x080003dc   0x000002e8   Code   RO           87    .text               max262.o
    0x080006c4   0x080006c4   0x0000035c   Code   RO          115    .text               stm32f10x_gpio.o
    0x08000a20   0x08000a20   0x000003a4   Code   RO          127    .text               stm32f10x_rcc.o
    0x08000dc4   0x08000dc4   0x000001e0   Code   RO          164    .text               system_stm32f10x.o
    0x08000fa4   0x08000fa4   0x00000040   Code   RO          185    .text               startup_stm32f10x_hd.o
    0x08000fe4   0x08000fe4   0x00000006   Code   RO          191    .text               c_w.l(heapauxi.o)
    0x08000fea   0x08000fea   0x0000004a   Code   RO          230    .text               c_w.l(sys_stackheap_outer.o)
    0x08001034   0x08001034   0x00000012   Code   RO          232    .text               c_w.l(exit.o)
    0x08001046   0x08001046   0x00000002   PAD
    0x08001048   0x08001048   0x00000008   Code   RO          240    .text               c_w.l(libspace.o)
    0x08001050   0x08001050   0x0000000c   Code   RO          301    .text               c_w.l(sys_exit.o)
    0x0800105c   0x0800105c   0x00000002   Code   RO          312    .text               c_w.l(use_no_semi.o)
    0x0800105e   0x0800105e   0x00000000   Code   RO          314    .text               c_w.l(indicate_semi.o)
    0x0800105e   0x0800105e   0x00000002   PAD
    0x08001060   0x08001060   0x000000c4   Code   RO          195    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x08001124   0x08001124   0x00000184   Code   RO          202    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x080012a8   0x080012a8   0x0000003e   Code   RO          205    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x080012e6   0x080012e6   0x0000008c   Code   RO          210    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08001372   0x08001372   0x0000000a   Code   RO          212    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x0800137c   0x0800137c   0x00000014   Code   RO          196    x$fpl$frsb          fz_ws.l(faddsub_clz.o)
    0x08001390   0x08001390   0x000000ea   Code   RO          197    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x0800147a   0x0800147a   0x00000000   Code   RO          214    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x0800147a   0x0800147a   0x00000002   PAD
    0x0800147c   0x0800147c   0x00000020   Data   RO          369    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800149c, Size: 0x00000688, Max: 0x0000c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800149c   0x00000014   Data   RW          128    .data               stm32f10x_rcc.o
    0x20000014   0x080014b0   0x00000014   Data   RW          165    .data               system_stm32f10x.o
    0x20000028        -       0x00000060   Zero   RW          241    .bss                c_w.l(libspace.o)
    0x20000088        -       0x00000200   Zero   RW          183    HEAP                startup_stm32f10x_hd.o
    0x20000288        -       0x00000400   Zero   RW          182    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0         32   core_cm3.o
       296          0          0          0          0       2621   delay.o
        56         12          0          0          0     225539   main.o
       744          4          0          0          0       1889   max262.o
       176         10          0          0          0        911   peripheralinit.o
        64         26        304          0       1536        892   startup_stm32f10x_hd.o
       860         38          0          0          0       5905   stm32f10x_gpio.o
       932         36          0         20          0       9156   stm32f10x_rcc.o
       480         38          0         20          0       2111   system_stm32f10x.o

    ----------------------------------------------------------------------
      3608        <USER>        <GROUP>         40       1536     249056   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         0          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
       450          8          0          0          0        236   faddsub_clz.o
       388         76          0          0          0         96   fdiv.o
        62          4          0          0          0         84   ffixu.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      1332        <USER>          <GROUP>          0         96       1152   Library Totals
        10          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       272         16          0          0         96        584   c_w.l
      1050         92          0          0          0        568   fz_ws.l

    ----------------------------------------------------------------------
      1332        <USER>          <GROUP>          0         96       1152   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      4940        272        336         40       1632     249112   Grand Totals
      4940        272        336         40       1632     249112   ELF Image Totals
      4940        272        336         40          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 5276 (   5.15kB)
    Total RW  Size (RW Data + ZI Data)              1672 (   1.63kB)
    Total ROM Size (Code + RO Data + RW Data)       5316 (   5.19kB)

==============================================================================

